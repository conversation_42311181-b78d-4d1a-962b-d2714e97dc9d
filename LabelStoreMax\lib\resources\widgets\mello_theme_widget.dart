//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import 'dart:ui';
import '/resources/widgets/product_item_container_widget.dart';
import '/resources/pages/browse_category_page.dart';
import '/resources/pages/home_search_page.dart';
import '/resources/pages/product_detail_page.dart';
import '/resources/widgets/cached_image_widget.dart';
import '/resources/widgets/top_nav_widget.dart';
import 'package:flutter_swiper_view/flutter_swiper_view.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/home_drawer_widget.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/woocommerce_service.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/woocommerce_wrappers/my_product_category.dart';
import '/app/models/bottom_nav_item.dart';
import '/bootstrap/app_helper.dart';
import '/resources/pages/cart_page.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/app/models/cart.dart';
import '/app/models/cart_line_item.dart';
import '/resources/pages/categories_page.dart';
import '/resources/pages/settings_page.dart';
import '/resources/themes/styles/design_constants.dart';
import '/bootstrap/extensions.dart';


class MelloThemeWidget extends StatefulWidget {
  const MelloThemeWidget({super.key, this.appConfig});
  final dynamic appConfig; // Temporarily dynamic to avoid breaking changes

  @override
  createState() => _MelloThemeWidgetState();
}

class _MelloThemeWidgetState extends NyState<MelloThemeWidget> {
  List<MyProductCategory> _categories = [];
  Widget? activeWidget;
  int _currentIndex = 0;
  List<BottomNavItem> allNavWidgets = [];

  @override
  get init => () async {
        await _fetchCategories();
        await _loadTabs();
      };

  _fetchCategories() async {
    try {
      _categories = await WooCommerceService().getProductCategories(
        parent: 0,
        perPage: 50,
        hideEmpty: true,
      );
      // Sort by name for now (can be customized later)
      _categories.sort((category1, category2) =>
          category1.name.compareTo(category2.name));
    } catch (e) {
      NyLogger.error("Error fetching categories: $e");
      _categories = [];
    }
  }

  _loadTabs() async {
    allNavWidgets = await bottomNavWidgets();
    if (allNavWidgets.isNotEmpty) {
      activeWidget = allNavWidgets[0].tabWidget;
    }
    setState(() {});
  }

  _changeMainWidget(int currentIndex, List<BottomNavItem> allNavWidgets) {
    // Smooth page transition with elegant animation
    if (_currentIndex != currentIndex) {
      setState(() {
        _currentIndex = currentIndex;
        activeWidget = _buildAnimatedPageTransition(
          allNavWidgets[currentIndex].tabWidget,
          currentIndex,
        );
      });
    }
  }

  /// Creates smooth page transitions with Hero animations
  Widget _buildAnimatedPageTransition(Widget child, int index) {
    return AnimatedSwitcher(
      duration: DesignConstants.normalAnimation,
      switchInCurve: DesignConstants.elegantCurve,
      switchOutCurve: DesignConstants.elegantCurve,
      transitionBuilder: (Widget child, Animation<double> animation) {
        // Different transition effects for different pages
        switch (index) {
          case 0: // Home - Slide from bottom
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 0.3),
                end: Offset.zero,
              ).animate(animation),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          case 1: // Categories - Slide from right
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.3, 0.0),
                end: Offset.zero,
              ).animate(animation),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          case 2: // Cart - Scale and fade
            return ScaleTransition(
              scale: Tween<double>(
                begin: 0.8,
                end: 1.0,
              ).animate(animation),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          case 3: // Search - Slide from left
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(-0.3, 0.0),
                end: Offset.zero,
              ).animate(animation),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          default:
            return FadeTransition(
              opacity: animation,
              child: child,
            );
        }
      },
      child: Hero(
        tag: 'page_$index',
        child: child,
      ),
    );
  }

  Future<List<BottomNavItem>> bottomNavWidgets() async {
    List<BottomNavItem> items = [];

    // Home - الرئيسية (Elegant line art house)
    items.add(
      BottomNavItem(
          id: 1,
          bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined, size: DesignConstants.iconMedium),
            activeIcon: Icon(Icons.home_rounded, size: DesignConstants.iconMedium),
            label: 'الرئيسية',
          ),
          tabWidget: _buildHomeWidget()),
    );

    // Categories - الفئات (Refined stacked boxes/tiers)
    items.add(
      BottomNavItem(
          id: 2,
          bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Icon(Icons.dashboard_outlined, size: DesignConstants.iconMedium),
            activeIcon: Icon(Icons.dashboard_rounded, size: DesignConstants.iconMedium),
            label: 'الفئات',
          ),
          tabWidget: CategoriesPage()),
    );

    // Cart - السلة (Elegant shopping bag)
    items.add(BottomNavItem(
      id: 3,
      bottomNavigationBarItem: BottomNavigationBarItem(
          icon: Icon(Icons.shopping_bag_outlined, size: DesignConstants.iconMedium),
          activeIcon: Icon(Icons.shopping_bag, size: DesignConstants.iconMedium),
          label: 'السلة'),
      tabWidget: CartPage(),
    ));

    // Search - البحث (Elegant search glass)
    items.add(
      BottomNavItem(
          id: 4,
          bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Icon(Icons.search_outlined, size: DesignConstants.iconMedium),
            activeIcon: Icon(Icons.search_rounded, size: DesignConstants.iconMedium),
            label: 'البحث',
          ),
          tabWidget: HomeSearchPage()),
    );

    // Profile/Settings - الملف الشخصي (Elegant user profile)
    items.add(BottomNavItem(
      id: 5,
      bottomNavigationBarItem: BottomNavigationBarItem(
          icon: Icon(Icons.person_outline_rounded, size: DesignConstants.iconMedium),
          activeIcon: Icon(Icons.person_rounded, size: DesignConstants.iconMedium),
          label: 'الملف الشخصي'),
      tabWidget: SettingsPage(),
    ));

    return items;
  }

  Widget _buildHomeWidget() {
    // Load banner images from AppConfig
    List<String>? bannerImages = AppHelper.instance.appConfig?.bannerImages ?? [];
    return Scaffold(
      drawer: HomeDrawerWidget(),
      appBar: AppBar(
        title: Text(
          'Velvete Store',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).appBarTheme.titleTextStyle?.color,
          ),
        ),
        centerTitle: true,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(
              Icons.menu,
              size: 28,
              color: Theme.of(context).appBarTheme.iconTheme?.color,
            ),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.notifications_outlined,
              size: 28,
              color: Theme.of(context).appBarTheme.iconTheme?.color,
            ),
            onPressed: () {
              // Navigate to notifications page when implemented
              showToast(
                title: "الإشعارات",
                description: "ستتوفر صفحة الإشعارات قريباً",
                style: ToastNotificationStyleType.info,
              );
            },
          ),
          Stack(
            children: [
              IconButton(
                icon: Icon(
                  Icons.shopping_bag_outlined,
                  size: 28,
                  color: Theme.of(context).appBarTheme.iconTheme?.color,
                ),
                onPressed: () {
                  // Navigate to cart
                  _changeMainWidget(3, allNavWidgets); // Cart is at index 3
                },
              ),
              // Red badge for cart items
              FutureBuilder<List<CartLineItem>>(
                future: Cart.getInstance.getCart(),
                builder: (context, snapshot) {
                  int cartCount = snapshot.data?.length ?? 0;
                  if (cartCount == 0) return SizedBox.shrink();

                  return Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '$cartCount',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          IconButton(
            icon: Icon(
              Icons.search,
              size: 28,
              color: Theme.of(context).appBarTheme.iconTheme?.color,
            ),
            onPressed: () {
              routeTo(HomeSearchPage.path);
            },
          ),
        ],
      ),
      body: SafeAreaWidget(
        child: NyPullToRefresh.grid(
          header: Column(
            children: [
                // Carousel Slider - First thing on homepage
                Container(
                  height: 250,
                  margin: EdgeInsets.only(bottom: 16),
                  child: CarouselSlider(
                    options: CarouselOptions(
                      height: 250,
                      autoPlay: true,
                      autoPlayInterval: Duration(seconds: 4),
                      autoPlayAnimationDuration: Duration(milliseconds: 800),
                      autoPlayCurve: Curves.easeInOutCubic,
                      enlargeCenterPage: true,
                      enlargeFactor: 0.2,
                      viewportFraction: 0.9,
                      enableInfiniteScroll: true,
                    ),
                    items: [
                      'https://i.imgur.com/oDevWeY.jpeg',
                      'https://i.imgur.com/LZ1dpeC.jpeg',
                      'https://velvete.ly/wp-content/uploads/2025/05/Untitled-design.png',
                    ].map((imageUrl) {
                      return Builder(
                        builder: (BuildContext context) {
                          return Container(
                            width: MediaQuery.of(context).size.width,
                            margin: EdgeInsets.symmetric(horizontal: 5.0),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 8,
                                  offset: Offset(0, 4),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(16),
                              child: CachedImageWidget(
                                image: imageUrl,
                                fit: BoxFit.cover,
                                height: 250,
                                width: double.infinity,
                              ),
                            ),
                          );
                        },
                      );
                    }).toList(),
                  ),
                ),

                // Banner images (if any)
                if (bannerImages.isNotEmpty)
                  SizedBox(
                    height: 300,
                    child: Swiper(
                      itemBuilder: (BuildContext context, int index) {
                        return CachedImageWidget(
                          image: bannerImages[index],
                          fit: BoxFit.contain,
                        );
                      },
                      itemCount: bannerImages.length,
                      viewportFraction: 0.8,
                      scale: 0.9,
                    ),
                  ),

                // Top navigation widget
                TopNavWidget(onPressBrowseCategories: _modalBottomSheetMenu),
              ],
            ),
          child: (context, product) {
            product as MyProduct;
            return SizedBox(
              height: 300,
              child: ProductItemContainer(
                product: product,
                onTap: () => _showProduct(product),
              ),
            );
          },
          data: (page) async {
            try {
              return await WooCommerceService().getProducts(
                page: page,
                perPage: 50,
                status: WooFilterStatus.publish,
                stockStatus: WooProductStockStatus.instock,
              );
            } catch (e) {
              NyLogger.error("Error fetching products: $e");
              return <MyProduct>[];
            }
          },
        ),
      ),
    );
  }



  _modalBottomSheetMenu() {
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              trans("Categories"),
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 16),
            Flexible(
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: _categories.length,
                separatorBuilder: (cxt, i) => Divider(),
                itemBuilder: (BuildContext context, int index) => ListTile(
                  title: Text(parseHtmlString(_categories[index].name)),
                  onTap: () {
                    Navigator.pop(context);
                    routeTo(BrowseCategoryPage.path, data: _categories[index]);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      body: activeWidget ?? AppLoaderWidget(),
      resizeToAvoidBottomInset: false,
      // Debug test button removed - test page was deleted
      bottomNavigationBar: allNavWidgets.isEmpty
          ? AppLoaderWidget()
          : Container(
              decoration: BoxDecoration(
                color: Theme.of(context).bottomNavigationBarTheme.backgroundColor?.withValues(alpha: 0.95),
                boxShadow: DesignConstants.bottomNavShadow,
                // Frosted glass effect
                backgroundBlendMode: BlendMode.overlay,
              ),
              child: ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).bottomNavigationBarTheme.backgroundColor?.withValues(alpha: 0.8),
                    ),
              child: BottomNavigationBar(
                backgroundColor: Colors.transparent,
                onTap: (currentIndex) =>
                    _changeMainWidget(currentIndex, allNavWidgets),
                currentIndex: _currentIndex,
                selectedItemColor: context.color.bottomTabBarIconSelected,
                unselectedItemColor: context.color.bottomTabBarIconUnselected,
                type: BottomNavigationBarType.fixed,
                selectedLabelStyle: TextStyle(
                  color: context.color.bottomTabBarLabelSelected,
                  fontWeight: FontWeight.w600,
                  fontSize: DesignConstants.labelMedium,
                ),
                unselectedLabelStyle: TextStyle(
                  color: context.color.bottomTabBarLabelUnselected,
                  fontSize: DesignConstants.labelMedium,
                  fontWeight: FontWeight.w400,
                ),
                showSelectedLabels: true,
                showUnselectedLabels: true,
                elevation: 0, // Remove default elevation, using custom shadow
                items:
                    allNavWidgets.map((e) => e.bottomNavigationBarItem).toList(),
              ),
                  ),
                ),
              ),
            ),
    );
  }

  _showProduct(MyProduct product) {
    routeTo(ProductDetailPage.path, data: product);
  }
}
